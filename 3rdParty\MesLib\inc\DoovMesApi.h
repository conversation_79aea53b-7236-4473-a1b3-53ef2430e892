#pragma once
#ifdef DOOVMESAPI_EXPORTS
#define DOOVMESAPI_API __declspec(dllexport)
#else
#define DOOVMESAPI_API __declspec(dllimport)
#endif

#include <map>
#include <list>
#include <vector>
#include <string>
#include <iostream>
using namespace std;

#define MAX_BASE_SIZE 64
#define MAX_BUFFER_SIZE 1024

struct WorkStationStr 
{
	string ID;
	string SN;
	string Name;
};

struct MoNoListStr
{
	string OrgId;
	string MoNo;
};

typedef struct
{
	char SN[MAX_BASE_SIZE];
	char <PERSON>N[MAX_BASE_SIZE];
	char IMEI1[MAX_BASE_SIZE];
	char IMEI2[MAX_BASE_SIZE];
	char Bt[MAX_BASE_SIZE];
	char Wifi[MAX_BASE_SIZE];
	char NetCode[MAX_BASE_SIZE];
	char <PERSON>Key[MAX_BUFFER_SIZE];
}MesCode_struct;

typedef struct
{
	char Line[MAX_BASE_SIZE];
	char WorkStationSn[MAX_BASE_SIZE];
	char MoNo[MAX_BASE_SIZE];
	char OrgId[MAX_BASE_SIZE];
	char userCode[MAX_BASE_SIZE];
	char PCName[MAX_BASE_SIZE];
	char OperationID[MAX_BASE_SIZE];
	char DutCode[MAX_BASE_SIZE];
	char TestResult[MAX_BASE_SIZE];
}PostInfo_struct;

/*
	Exprot API
*/
DOOVMESAPI_API bool __stdcall Fn_AutoTest_GetLine(PostInfo_struct* PostInfo, char* ErrInfo, map<string, string>* AllLine);

DOOVMESAPI_API bool __stdcall Fn_AutoTest_GetWorkStation(PostInfo_struct* PostInfo, char* ErrInfo, vector<WorkStationStr>* AllStation);

DOOVMESAPI_API bool __stdcall Fn_AutoTest_GetMoList(PostInfo_struct* PostInfo, char* ErrInfo, vector<MoNoListStr>* AllList);

DOOVMESAPI_API bool __stdcall Fn_AutoTest_GetMoInfo(PostInfo_struct* PostInfo, char* ErrInfo, map<string, string>* AllInfo);

DOOVMESAPI_API bool __stdcall Fn_AutoTest_CheckProPass(PostInfo_struct* PostInfo, char* ErrInfo, map<string, string>* AllData);

DOOVMESAPI_API bool __stdcall Fn_AutoTest_GetCodesByImei(PostInfo_struct* PostInfo, char* ErrInfo, map<string, string>* AllData);

DOOVMESAPI_API bool __stdcall Fn_AutoTest_MoveStation(PostInfo_struct* PostInfo, char* ErrInfo, map<string, string>* AllData);
