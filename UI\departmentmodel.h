#ifndef DEPARTMENTMODEL_H
#define DEPARTMENTMODEL_H

#include <QAbstractItemModel>
#include <QVector>

struct DepartmentNode {
    QString name;                  // 部门名称（如“财务综合中心”）
    QVector<DepartmentNode*> children; // 子部门
    DepartmentNode *parent = nullptr;  // 父部门
    ~DepartmentNode() { qDeleteAll(children); } // 自动释放子节点
};

class DepartmentModel : public QAbstractItemModel
{
    Q_OBJECT
public:
    explicit DepartmentModel(QObject *parent = nullptr);
    ~DepartmentModel();
    void setDepartments(const QStringList &departments); // 批量设置部门（含层级）

    // QAbstractItemModel 强制重写接口
    QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const override;
    QModelIndex parent(const QModelIndex &child) const override;
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

private:
    DepartmentNode *rootNode; // 根节点（无实际意义，仅作容器）
    DepartmentNode* findOrCreateParent(DepartmentNode *parent, const QString &deptPart);
};

#endif // DEPARTMENTMODEL_H
