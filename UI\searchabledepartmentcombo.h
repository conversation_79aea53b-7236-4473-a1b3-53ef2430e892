#ifndef SEARCHABLEDEPARTMENTCOMBO_H
#define SEARCHABLEDEPARTMENTCOMBO_H

#include <QWidget>
#include <QLineEdit>
#include <QToolButton>
#include <QListView>
#include <QFrame>
#include "departmentmodel.h"
#include "departmentfilterproxymodel.h"
#include "departmentitemdelegate.h"

class SearchableDepartmentCombo : public QWidget
{
    Q_OBJECT
public:
    explicit SearchableDepartmentCombo(QWidget *parent = nullptr);
    void setDepartments(const QStringList &departments); // 同步设置部门数据
    void setPlaceholderText(const QString &text);       // 设置搜索框占位符

signals:
    void departmentSelected(const QString &dept);       // 选中部门时触发

protected:
    bool eventFilter(QObject *obj, QEvent *event) override; // 点击外部隐藏弹出
    void resizeEvent(QResizeEvent *event) override;         // 调整搜索按钮位置

private slots:
    void onSearchTextChanged(const QString &text); // 搜索文本变化时过滤
    void onItemClicked(const QModelIndex &index);  // 列表项点击时处理
    void showPopup();                              // 显示下拉列表
    void hidePopup();                              // 隐藏下拉列表

private:
    QLineEdit *m_lineEdit;           // 搜索输入框
    QToolButton *m_searchButton;     // 搜索图标按钮
    QFrame *m_popupFrame;            // 下拉弹出框
    QListView *m_listView;           // 部门列表视图
    DepartmentModel *m_sourceModel;  // 原始层级数据模型
    DepartmentFilterProxyModel *m_proxyModel; // 搜索过滤模型
    bool m_popupVisible = false;     // 下拉框是否可见
};

#endif // SEARCHABLEDEPARTMENTCOMBO_H
