#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "UI/searchabledepartmentcombo.h"
#include <QVBoxLayout>
#include <QLabel>
#include <QMessageBox>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);

    QWidget *centralWidget = new QWidget(this);
    QVBoxLayout *layout = new QVBoxLayout(centralWidget);

    // 添加标题
    QLabel *titleLabel = new QLabel("部门归属", this);
    titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;");
    layout->addWidget(titleLabel);

    // 创建部门搜索下拉框
    m_departmentCombo = new SearchableDepartmentCombo(this);
    m_departmentCombo->setPlaceholderText("搜索部门...");

    // 设置部门数据（模拟您图片中的层级结构）
    QStringList departments;
    departments << "财务综合中心"
                << "财务综合中心-财务资金部"
                << "财务综合中心-无锡财务综合部"
                << "软件研发中心"
                << "软件研发中心-软件服务与支持部"
                << "软件研发中心-软件项目部"
                << "软件研发中心-系统驱动部"
                << "软件研发中心-应用开发部"
                << "市场推广部"
                << "研发管理中心-项目管理部"
                << "研发管理中心-移动研发二部";

    m_departmentCombo->setDepartments(departments);

    // 连接信号
    connect(m_departmentCombo, &SearchableDepartmentCombo::departmentSelected,
            this, &MainWindow::onDepartmentSelected);

    layout->addWidget(m_departmentCombo);
    layout->addStretch(); // 添加弹性空间

    setCentralWidget(centralWidget);
    resize(500, 400);
    setWindowTitle("部门搜索下拉框演示");
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::onDepartmentSelected(const QString &dept)
{
    QMessageBox::information(this, "部门选择", QString("您选择了部门：%1").arg(dept));
}
