#include "Searchablecombobox.h"
#include <QRegExp>

SearchableComboBox::SearchableComboBox(QWidget *parent)
    : QComboBox(parent)
{
    // 设置可编辑并获取LineEdit
    setEditable(true);
    QLineEdit *lineEdit = this->lineEdit();
    lineEdit->setClearButtonEnabled(true);

    // 创建原始模型和代理模型
    sourceModel = new QStringListModel(this);
    proxyModel = new QSortFilterProxyModel(this);
    proxyModel->setSourceModel(sourceModel);
    proxyModel->setFilterCaseSensitivity(Qt::CaseInsensitive); // 不区分大小写

    // 设置代理模型到ComboBox
    setModel(proxyModel);

    // 连接文本变化信号
    connect(lineEdit, &QLineEdit::textChanged, this, &SearchableComboBox::onTextChanged);
}

void SearchableComboBox::addItems(const QStringList &items) {
    sourceModel->setStringList(items);
    proxyModel->setSourceModel(sourceModel);
}

void SearchableComboBox::setPlaceholderText(const QString &text) {
    lineEdit()->setPlaceholderText(text);
}

void SearchableComboBox::showPopup() {
    popupVisible = true;
    lineEdit()->selectAll(); // 全选文本方便直接搜索
    QComboBox::showPopup();
}

void SearchableComboBox::hidePopup() {
    popupVisible = false;
    proxyModel->setFilterRegExp(QRegExp("", Qt::CaseInsensitive, QRegExp::FixedString)); // 关闭时重置过滤器
    QComboBox::hidePopup();
}

void SearchableComboBox::onTextChanged(const QString &text) {
    if (popupVisible) {
        // 只在弹出状态下应用筛选
        proxyModel->setFilterRegExp(QRegExp(text, Qt::CaseInsensitive, QRegExp::FixedString));
    }
}

#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QMouseEvent>
#include <QScreen>

SearchableDepartmentCombo::SearchableDepartmentCombo(QWidget *parent)
    : QWidget(parent) {
    // ========== 初始化搜索输入区域 ==========
    m_lineEdit = new QLineEdit(this);
    m_lineEdit->setClearButtonEnabled(true);
    m_lineEdit->setPlaceholderText("搜索部门...");
    m_lineEdit->setStyleSheet(R"(
        QLineEdit {
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 4px 28px 4px 8px; /* 右侧留空间给搜索按钮 */
        }
        QLineEdit::clear-button {
            subcontrol-position: right;
            subcontrol-origin: padding;
            width: 16px; height: 16px;
        }
    )");

    m_searchButton = new QToolButton(this);
    m_searchButton->setText("🔍"); // 或用资源文件图标（如 ":/icons/search.png"）
    m_searchButton->setStyleSheet("QToolButton { border: none; }");
    m_searchButton->setFixedSize(24, 24);
    connect(m_searchButton, &QToolButton::clicked, this, &SearchableDepartmentCombo::showPopup);
    connect(m_lineEdit, &QLineEdit::returnPressed, this, &SearchableDepartmentCombo::showPopup);
    connect(m_lineEdit, &QLineEdit::textChanged, this, &SearchableDepartmentCombo::onSearchTextChanged);

    QHBoxLayout *inputLayout = new QHBoxLayout;
    inputLayout->addWidget(m_lineEdit);
    inputLayout->addWidget(m_searchButton);
    inputLayout->setContentsMargins(0, 0, 0, 0);
    inputLayout->setAlignment(m_searchButton, Qt::AlignRight);

    // ========== 初始化下拉弹出框 ==========
    m_popupFrame = new QFrame(this);
    m_popupFrame->setWindowFlags(Qt::Popup);
    m_popupFrame->setStyleSheet(R"(
        QFrame { background: white; border: 1px solid #ccc; border-radius: 4px; }
    )");

    m_listView = new QListView(m_popupFrame);
    m_listView->setStyleSheet(R"(
        QListView { border: none; }
        QListView::item { padding: 4px; }
        QListView::item:selected {
            background: #0078d7;
            color: white;
        }
    )");
    m_listView->setItemDelegate(new DepartmentItemDelegate(this)); // 层级缩进委托
    connect(m_listView, &QListView::clicked, this, &SearchableDepartmentCombo::onItemClicked);

    QVBoxLayout *popupLayout = new QVBoxLayout(m_popupFrame);
    popupLayout->addWidget(m_listView);
    popupLayout->setContentsMargins(0, 0, 0, 0);

    // ========== 初始化数据模型 ==========
    m_sourceModel = new QStandardItemModel(this);
    m_proxyModel = new DepartmentFilterProxyModel(this);
    m_proxyModel->setSourceModel(m_sourceModel);
    m_proxyModel->setFilterCaseSensitivity(Qt::CaseInsensitive);
    m_listView->setModel(m_proxyModel);

    // ========== 主布局与事件过滤 ==========
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addLayout(inputLayout);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // 点击外部隐藏弹出框
    installEventFilter(this);
    m_lineEdit->installEventFilter(this);
    m_searchButton->installEventFilter(this);
    m_listView->installEventFilter(this);
    m_popupFrame->installEventFilter(this);
}

void SearchableDepartmentCombo::setDepartments(const QStringList &departments) {
    m_sourceModel->clear();
    for (const QString &dept : departments) {
        // 拆分部门层级（如 "财务综合中心-财务资金部" → 父："财务综合中心"，子："财务资金部"）
        QStringList parts = dept.split("-");
        QStandardItem *parentItem = nullptr;

        for (int i = 0; i < parts.size(); ++i) {
            QString currentDept = parts.mid(0, i + 1).join("-"); // 逐层构建完整部门名
            QModelIndex index = m_sourceModel->match(
                                                 m_sourceModel->index(0, 0),
                                                 Qt::DisplayRole,
                                                 currentDept,
                                                 1,
                                                 Qt::MatchExactly | Qt::MatchRecursive
                                                 ).first();

            if (index.isValid()) {
                parentItem = m_sourceModel->itemFromIndex(index);
            } else {
                QStandardItem *item = new QStandardItem(dept); // 最终显示完整部门名
                if (parentItem) {
                    parentItem->appendRow(item);
                } else {
                    m_sourceModel->appendRow(item);
                }
                parentItem = item;
            }
        }
    }
}

void SearchableDepartmentCombo::setPlaceholderText(const QString &text) {
    m_lineEdit->setPlaceholderText(text);
}

bool SearchableDepartmentCombo::eventFilter(QObject *obj, QEvent *event) {
    if (m_popupVisible && event->type() == QEvent::MouseButtonPress) {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
        QPoint popupPos = m_popupFrame->mapToGlobal(QPoint(0, 0));
        QRect popupRect(popupPos, m_popupFrame->size());
        if (!popupRect.contains(mouseEvent->globalPos())) {
            hidePopup();
            return true;
        }
    }
    return QWidget::eventFilter(obj, event);
}

void SearchableDepartmentCombo::onSearchTextChanged(const QString &text) {
    if (m_popupVisible) {
        m_proxyModel->setFilterRegExp(QRegExp(text, Qt::CaseInsensitive, QRegExp::FixedString));
    }
}

void SearchableDepartmentCombo::onItemClicked(const QModelIndex &index) {
    QString dept = m_proxyModel->data(index, Qt::DisplayRole).toString();
    m_lineEdit->setText(dept);
    emit departmentSelected(dept);
    hidePopup();
}

void SearchableDepartmentCombo::showPopup() {
    if (m_popupVisible) return;
    m_popupVisible = true;

    // 计算弹出位置（在搜索框正下方）
    QPoint pos = mapToGlobal(m_lineEdit->pos());
    pos.setY(pos.y() + m_lineEdit->height());
    m_popupFrame->move(pos);

    // 宽度与搜索框一致，高度自适应内容（最多200px）
    int width = m_lineEdit->width();
    int height = qMin(m_listView->model()->rowCount() * 24, 200); // 每项约24px
    m_popupFrame->setFixedSize(width, height);
    m_popupFrame->show();

    m_lineEdit->selectAll(); // 全选文本，方便直接输入搜索
}

void SearchableDepartmentCombo::hidePopup() {
    if (!m_popupVisible) return;
    m_popupVisible = false;
    m_popupFrame->hide();
    m_proxyModel->setFilterRegExp(QRegExp("", Qt::CaseInsensitive, QRegExp::FixedString)); // 清空搜索过滤
}
