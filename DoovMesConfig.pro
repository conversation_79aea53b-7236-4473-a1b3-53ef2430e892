QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

QMAKE_CXXFLAGS += /utf-8
DEFINES -= UNICODE
DEFINES += UMBCS
QMAKE_CXXFLAGS -= -Zc:strictStrings

LIBS += -lshlwapi

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

# 程序版本
VERSION = 1.0
# 公司名称
QMAKE_TARGET_COMPANY = "Agenew"
# 程序说明
QMAKE_TARGET_DESCRIPTION = "Doov MesConfig Setting Tool"
# 版权信息
QMAKE_TARGET_COPYRIGHT = "Copyright (C) 2025 Agenew"
# 程序名称
QMAKE_TARGET_PRODUCT = "Doov_MesSetting_Tool"

SOURCES += \
    UI/DepartmentItemDelegate.cpp \
    UI/Departmentfilterproxymodel.cpp \
    UI/Searchablecombobox.cpp \
    UI/departmentmodel.cpp \
    UI/searchabledepartmentcombo.cpp \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    UI/DepartmentFilterProxyModel.h \
    UI/DepartmentItemDelegate.h \
    UI/Searchablecombobox.h \
    UI/departmentmodel.h \
    UI/searchabledepartmentcombo.h \
    mainwindow.h

FORMS += \
    mainwindow.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

build_type =
CONFIG(debug, debug|release) {
    build_type = debug
} else {
    build_type = release
}

OUT_PWD = $$PWD/Output
DESTDIR = $$PWD/Output/$$build_type/bin
MOC_DIR = $$PWD/Output/$$build_type/moc
OBJECTS_DIR = $$PWD/Output/$$build_type/obj
UI_DIR = $$PWD/Output/$$build_type/ui
RCC_DIR = $$PWD/Output/$$build_type/qrc

LIBS += -L$$PWD/3rdParty/MesLib/lib/ -lDoovMesService

INCLUDEPATH += $$PWD/3rdParty/MesLib/inc

DEPENDPATH += $$PWD/3rdParty/MesLib/lib
		
