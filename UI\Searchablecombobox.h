#ifndef SEARCHABLECOMBOBOX_H
#define SEARCHABLECOMBOBOX_H

#include <QComboBox>
#include <QLineEdit>
#include <QSortFilterProxyModel>
#include <QStringListModel>
#include <QApplication>

class SearchableComboBox : public QComboBox {
    Q_OBJECT
public:
    explicit SearchableComboBox(QWidget *parent = nullptr);
    void addItems(const QStringList &items);
    void setPlaceholderText(const QString &text);

protected:
    void showPopup() override;
    void hidePopup() override;

private slots:
    void onTextChanged(const QString &text);

private:
    QSortFilterProxyModel *proxyModel;
    QStringListModel *sourceModel;
    bool popupVisible = false;
};

#include <QWidget>
#include <QLineEdit>
#include <QToolButton>
#include <QListView>
#include <QStandardItemModel>
#include <QFrame>
#include <QSortFilterProxyModel>
#include <QStyledItemDelegate>

class DepartmentFilterProxyModel : public QSortFilterProxyModel {
    Q_OBJECT
public:
    using QSortFilterProxyModel::QSortFilterProxyModel;
protected:
    // 重写过滤逻辑：父项若有子项匹配，则父项也显示；子项匹配则自身显示
    bool filterAcceptsRow(int sourceRow, const QModelIndex &sourceParent) const override {
        QModelIndex index = sourceModel()->index(sourceRow, 0, sourceParent);
        // 1. 检查当前项是否匹配关键词
        bool currentMatch = sourceModel()->data(index, filterRole()).toString()
                                .contains(QSortFilterProxyModel::filterFixedString(), Qt::CaseInsensitive);
        if (currentMatch) return true;

        // 2. 检查子项是否有匹配（父项递归逻辑）
        if (sourceModel()->hasChildren(index)) {
            for (int i = 0; i < sourceModel()->rowCount(index); ++i) {
                if (filterAcceptsRow(i, index)) {
                    return true; // 子项有匹配，父项显示
                }
            }
        }
        return false;
    }
};

class DepartmentItemDelegate : public QStyledItemDelegate {
    Q_OBJECT
public:
    using QStyledItemDelegate::QStyledItemDelegate;
    void paint(QPainter *painter, const QStyleOptionViewItem &option,
               const QModelIndex &index) const override {
        QStyleOptionViewItem opt = option;
        initStyleOption(&opt, index);

        // 计算层级：根据父节点深度（每级缩进 16px）
        int level = 0;
        QModelIndex parent = index.parent();
        while (parent.isValid()) {
            level++;
            parent = parent.parent();
        }
        opt.rect.translate(level * 16, 0); // 层级缩进

        QApplication::style()->drawControl(QStyle::CE_ItemViewItem, &opt, painter);
    }
};

class SearchableDepartmentCombo : public QWidget {
    Q_OBJECT
public:
    explicit SearchableDepartmentCombo(QWidget *parent = nullptr);
    void setDepartments(const QStringList &departments); // 设置部门列表（含层级，如"财务综合中心-财务资金部"）
    void setPlaceholderText(const QString &text);         // 设置搜索框占位符

signals:
    void departmentSelected(const QString &dept);         // 选中部门时触发

protected:
    bool eventFilter(QObject *obj, QEvent *event) override; // 点击外部隐藏弹出

private slots:
    void onSearchTextChanged(const QString &text); // 搜索文本变化时过滤
    void onItemClicked(const QModelIndex &index);  // 列表项点击时处理
    void showPopup();                              // 显示下拉列表
    void hidePopup();                              // 隐藏下拉列表

private:
    QLineEdit *m_lineEdit;           // 搜索输入框
    QToolButton *m_searchButton;     // 搜索图标按钮
    QFrame *m_popupFrame;            // 下拉弹出框
    QListView *m_listView;           // 部门列表视图
    QStandardItemModel *m_sourceModel; // 原始层级数据模型
    DepartmentFilterProxyModel *m_proxyModel; // 搜索过滤模型
    bool m_popupVisible = false;     // 下拉框是否可见
};

#endif // SEARCHABLECOMBOBOX_H
