#include "departmentitemdelegate.h"
#include <QPainter>
#include <QApplication>  // 必须包含，解决 QApplication 未定义

DepartmentItemDelegate::DepartmentItemDelegate(QObject *parent)
    : QStyledItemDelegate(parent)
{
}

void DepartmentItemDelegate::paint(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    QStyleOptionViewItem opt = option;
    initStyleOption(&opt, index);

    // 计算层级（每级缩进 20px）
    int level = 0;
    QModelIndex parentIndex = index.parent();
    while (parentIndex.isValid()) {
        level++;
        parentIndex = parentIndex.parent();
    }

    // 保存原始矩形
    QRect originalRect = opt.rect;

    // 应用缩进
    int indent = level * 20;
    opt.rect.adjust(indent, 0, 0, 0);

    // 绘制背景
    if (opt.state & QStyle::State_Selected) {
        painter->fillRect(originalRect, opt.palette.highlight());
    } else if (opt.state & QStyle::State_MouseOver) {
        painter->fillRect(originalRect, QColor(245, 245, 245));
    }

    // 绘制层级指示器（子项前面添加小圆点）
    if (level > 0) {
        painter->save();
        painter->setPen(QColor(150, 150, 150));
        painter->setBrush(QColor(150, 150, 150));
        int dotX = originalRect.left() + indent - 10;
        int dotY = originalRect.center().y();
        painter->drawEllipse(dotX, dotY - 2, 4, 4);
        painter->restore();
    }

    // 绘制文本
    painter->save();
    if (opt.state & QStyle::State_Selected) {
        painter->setPen(opt.palette.highlightedText().color());
    } else {
        painter->setPen(opt.palette.text().color());
    }

    QFont font = opt.font;
    if (level == 0) {
        font.setBold(true); // 顶级部门加粗
    }
    painter->setFont(font);

    painter->drawText(opt.rect, Qt::AlignLeft | Qt::AlignVCenter, opt.text);
    painter->restore();
}
