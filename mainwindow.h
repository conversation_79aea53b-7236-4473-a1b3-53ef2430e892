#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

class SearchableDepartmentCombo;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onDepartmentSelected(const QString &dept);

private:
    Ui::MainWindow *ui;
    SearchableDepartmentCombo *m_departmentCombo;
};
#endif // MAINWINDOW_H
