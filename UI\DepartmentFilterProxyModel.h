#ifndef DEPARTMENTFILTERPROXYMODEL_H
#define DEPARTMENTFILTERPROXYMODEL_H

#include <QSortFilterProxyModel>

class DepartmentFilterProxyModel : public QSortFilterProxyModel
{
    Q_OBJECT
public:
    explicit DepartmentFilterProxyModel(QObject *parent = nullptr);

    void setFilterString(const QString &filterString);
    QString getFilterString() const;

protected:
    bool filterAcceptsRow(int sourceRow, const QModelIndex &sourceParent) const override;

private:
    QString m_filterString;
};

#endif // DEPARTMENTFILTERPROXYMODEL_H
