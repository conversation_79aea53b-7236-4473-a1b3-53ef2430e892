#include "searchabledepartmentcombo.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QMouseEvent>
#include <QResizeEvent>
#include <QScreen>


SearchableDepartmentCombo::SearchableDepartmentCombo(QWidget *parent)
    : QWidget(parent)
{
    // ========== 初始化搜索输入区域 ==========
    m_lineEdit = new QLineEdit(this);
    m_lineEdit->setClearButtonEnabled(true);
    m_lineEdit->setPlaceholderText("搜索部门...");
    m_lineEdit->setStyleSheet(R"(
        QLineEdit {
            border: 1px solid #d0d0d0;
            border-radius: 3px;
            padding: 6px 30px 6px 8px;
            font-size: 14px;
            background-color: white;
            min-height: 20px;
        }
        QLineEdit:focus {
            border: 2px solid #0078d7;
            outline: none;
        }
        QLineEdit::clear-button {
            subcontrol-position: right;
            subcontrol-origin: padding;
            width: 16px; height: 16px;
            margin-right: 25px;
        }
    )");

    m_searchButton = new QToolButton(this);
    m_searchButton->setText("🔍"); // 或用资源文件图标（如 ":/icons/search.png"）
    m_searchButton->setStyleSheet(R"(
        QToolButton {
            border: none;
            background: transparent;
            color: #666;
            font-size: 14px;
        }
        QToolButton:hover {
            background-color: #f0f0f0;
            border-radius: 2px;
        }
    )");
    m_searchButton->setFixedSize(24, 24);
    connect(m_searchButton, &QToolButton::clicked, this, &SearchableDepartmentCombo::showPopup);
    connect(m_lineEdit, &QLineEdit::returnPressed, this, &SearchableDepartmentCombo::showPopup);
    connect(m_lineEdit, &QLineEdit::textChanged, this, &SearchableDepartmentCombo::onSearchTextChanged);

    // 使用相对定位将搜索按钮放在输入框内部右侧
    QHBoxLayout *inputLayout = new QHBoxLayout;
    inputLayout->addWidget(m_lineEdit);
    inputLayout->setContentsMargins(0, 0, 0, 0);

    // 将搜索按钮定位到输入框右侧
    m_searchButton->setParent(m_lineEdit);
    connect(m_lineEdit, &QLineEdit::textChanged, [this]() {
        // 动态调整搜索按钮位置
        int buttonY = (m_lineEdit->height() - m_searchButton->height()) / 2;
        int buttonX = m_lineEdit->width() - m_searchButton->width() - 5;
        m_searchButton->move(buttonX, buttonY);
    });

    // ========== 初始化下拉弹出框 ==========
    m_popupFrame = new QFrame(this);
    m_popupFrame->setWindowFlags(Qt::Popup);
    m_popupFrame->setStyleSheet(R"(
        QFrame {
            background: white;
            border: 1px solid #d0d0d0;
            border-radius: 3px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    )");

    m_listView = new QListView(m_popupFrame);
    m_listView->setStyleSheet(R"(
        QListView {
            border: none;
            background: white;
            outline: none;
        }
        QListView::item {
            padding: 8px 12px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        QListView::item:hover {
            background: #f5f5f5;
        }
        QListView::item:selected {
            background: #0078d7;
            color: white;
        }
    )");
    m_listView->setUniformItemSizes(true); // 启用“均匀项大小”优化（Qt5 支持）
    m_listView->setItemDelegate(new DepartmentItemDelegate(this)); // 层级缩进委托
    connect(m_listView, &QListView::clicked, this, &SearchableDepartmentCombo::onItemClicked);

    QVBoxLayout *popupLayout = new QVBoxLayout(m_popupFrame);
    popupLayout->addWidget(m_listView);
    popupLayout->setContentsMargins(0, 0, 0, 0);

    // ========== 初始化数据模型 ==========
    m_sourceModel = new DepartmentModel(this);
    m_proxyModel = new DepartmentFilterProxyModel(this);
    m_proxyModel->setSourceModel(m_sourceModel);
    m_listView->setModel(m_proxyModel);

    // ========== 主布局与事件过滤 ==========
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addLayout(inputLayout);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // 点击外部隐藏弹出框
    installEventFilter(this);
    m_lineEdit->installEventFilter(this);
    m_searchButton->installEventFilter(this);
    m_listView->installEventFilter(this);
    m_popupFrame->installEventFilter(this);
}

void SearchableDepartmentCombo::setDepartments(const QStringList &departments)
{
    m_sourceModel->setDepartments(departments);
}

void SearchableDepartmentCombo::setPlaceholderText(const QString &text)
{
    m_lineEdit->setPlaceholderText(text);
}

bool SearchableDepartmentCombo::eventFilter(QObject *obj, QEvent *event)
{
    if (m_popupVisible && event->type() == QEvent::MouseButtonPress) {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
        QPoint popupPos = m_popupFrame->mapToGlobal(QPoint(0, 0));
        QRect popupRect(popupPos, m_popupFrame->size());
        if (!popupRect.contains(mouseEvent->globalPos())) {
            hidePopup();
            return true;
        }
    }
    return QWidget::eventFilter(obj, event);
}

void SearchableDepartmentCombo::onSearchTextChanged(const QString &text)
{
    if (m_popupVisible) {
        m_proxyModel->setFilterRegExp(QRegExp(text, Qt::CaseInsensitive, QRegExp::FixedString)); // Qt 5.14.2 兼容
    }
}

void SearchableDepartmentCombo::onItemClicked(const QModelIndex &index)
{
    QString dept = m_proxyModel->data(index, Qt::DisplayRole).toString();
    m_lineEdit->setText(dept);
    emit departmentSelected(dept);
    hidePopup();
}

void SearchableDepartmentCombo::showPopup()
{
    if (m_popupVisible) return;
    m_popupVisible = true;

    // 设置弹出框位置和大小
    QPoint globalPos = mapToGlobal(QPoint(0, height()));
    m_popupFrame->move(globalPos);
    m_popupFrame->resize(width(), 200);

    // 应用当前搜索过滤
    m_proxyModel->setFilterRegExp(QRegExp(m_lineEdit->text(), Qt::CaseInsensitive, QRegExp::FixedString)); // Qt 5.14.2 兼容

    // 显示弹出框
    m_popupFrame->show();
    m_popupFrame->raise();
    m_popupFrame->activateWindow();
}

void SearchableDepartmentCombo::hidePopup()
{
    if (!m_popupVisible) return;
    m_popupVisible = false;
    m_popupFrame->hide();
}

void SearchableDepartmentCombo::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);

    // 调整搜索按钮位置
    if (m_searchButton && m_lineEdit) {
        int buttonY = (m_lineEdit->height() - m_searchButton->height()) / 2;
        int buttonX = m_lineEdit->width() - m_searchButton->width() - 5;
        m_searchButton->move(buttonX, buttonY);
    }
}
