#include "departmentfilterproxymodel.h"
#include <QModelIndex>

DepartmentFilterProxyModel::DepartmentFilterProxyModel(QObject *parent)
    : QSortFilterProxyModel(parent)
{
    setFilterCaseSensitivity(Qt::CaseInsensitive); // 不区分大小写
}

void DepartmentFilterProxyModel::setFilterString(const QString &filterString)
{
    m_filterString = filterString;
    invalidateFilter(); // 触发重新过滤
}

QString DepartmentFilterProxyModel::getFilterString() const
{
    return m_filterString;
}

bool DepartmentFilterProxyModel::filterAcceptsRow(int sourceRow, const QModelIndex &sourceParent) const
{
    // 获取过滤字符串 (Qt 5.14.2 兼容)
    QString filterString = filterRegExp().pattern();

    // 如果没有过滤条件，显示所有项
    if (filterString.isEmpty()) {
        return true;
    }

    QModelIndex index = sourceModel()->index(sourceRow, 0, sourceParent);

    // 1. 检查当前项是否匹配关键词
    QString itemText = sourceModel()->data(index, Qt::DisplayRole).toString();
    bool currentMatch = itemText.contains(filterString, Qt::CaseInsensitive);

    if (currentMatch) {
        return true;
    }

    // 2. 检查子项是否有匹配（如果子项匹配，父项也应该显示）
    if (sourceModel()->hasChildren(index)) {
        for (int i = 0; i < sourceModel()->rowCount(index); ++i) {
            if (filterAcceptsRow(i, index)) {
                return true; // 子项匹配，父项显示
            }
        }
    }

    // 3. 检查父项是否匹配（如果父项匹配，子项也应该显示）
    QModelIndex parentIndex = sourceParent;
    while (parentIndex.isValid()) {
        QString parentText = sourceModel()->data(parentIndex, Qt::DisplayRole).toString();
        if (parentText.contains(filterString, Qt::CaseInsensitive)) {
            return true; // 父项匹配，子项显示
        }
        parentIndex = parentIndex.parent();
    }

    return false;
}
