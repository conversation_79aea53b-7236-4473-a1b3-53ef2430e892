#include "departmentmodel.h"
#include <QStringList>

DepartmentModel::DepartmentModel(QObject *parent)
    : QAbstractItemModel(parent), rootNode(new DepartmentNode)
{
}

DepartmentModel::~DepartmentModel()
{
    delete rootNode; // 递归释放所有子节点
}

void DepartmentModel::setDepartments(const QStringList &departments)
{
    beginResetModel(); // 开始重置模型（避免多次触发信号）
    delete rootNode;   // 清空旧数据
    rootNode = new DepartmentNode;

    for (const QString &dept : departments) {
        QStringList parts = dept.split("-"); // 拆分层级（如“财务综合中心-财务资金部”→ ["财务综合中心", "财务资金部"]）
        DepartmentNode *currentParent = rootNode;

        for (const QString &part : parts) {
            currentParent = findOrCreateParent(currentParent, part);
        }
    }
    endResetModel(); // 结束重置，触发视图更新
}

// 辅助函数：在父节点下查找或创建子节点（确保层级唯一）
DepartmentNode* DepartmentModel::findOrCreateParent(DepartmentNode *parent, const QString &deptPart)
{
    for (DepartmentNode *child : parent->children) {
        if (child->name == deptPart) {
            return child; // 已存在，直接返回
        }
    }
    // 不存在则创建新节点
    DepartmentNode *newNode = new DepartmentNode;
    newNode->name = deptPart;
    newNode->parent = parent;
    parent->children.append(newNode);
    return newNode;
}

// 以下是 QAbstractItemModel 接口实现（核心：通过树结构映射索引）
QModelIndex DepartmentModel::index(int row, int column, const QModelIndex &parent) const
{
    if (!hasIndex(row, column, parent)) return QModelIndex();
    DepartmentNode *parentNode = parent.isValid()
                                     ? static_cast<DepartmentNode*>(parent.internalPointer())
                                     : rootNode;
    if (row < 0 || row >= parentNode->children.size()) return QModelIndex();
    return createIndex(row, column, parentNode->children.at(row));
}

QModelIndex DepartmentModel::parent(const QModelIndex &child) const
{
    if (!child.isValid()) return QModelIndex();
    DepartmentNode *childNode = static_cast<DepartmentNode*>(child.internalPointer());
    DepartmentNode *parentNode = childNode->parent;
    if (parentNode == rootNode) return QModelIndex(); // 根节点的子节点无父索引
    // 父节点在其祖父节点的子列表中的位置
    return createIndex(parentNode->parent->children.indexOf(parentNode), 0, parentNode);
}

int DepartmentModel::rowCount(const QModelIndex &parent) const
{
    DepartmentNode *parentNode = parent.isValid()
    ? static_cast<DepartmentNode*>(parent.internalPointer())
    : rootNode;
    return parentNode->children.size();
}

int DepartmentModel::columnCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent);
    return 1; // 仅1列（部门名称）
}

QVariant DepartmentModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || role != Qt::DisplayRole) return QVariant();
    DepartmentNode *node = static_cast<DepartmentNode*>(index.internalPointer());
    return node->name;
}
